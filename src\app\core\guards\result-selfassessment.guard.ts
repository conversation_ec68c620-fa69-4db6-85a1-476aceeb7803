import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { StoreService } from '../services';
import { AppRoutes, ID_AUTOLIQUIDACIO_PARAM } from '../models';

@Injectable({ providedIn: 'root' })
export class ResultSelfAssessmentGuard {
  constructor(private router: Router, private store: StoreService) {}

  canActivate(route: ActivatedRouteSnapshot) {
    console.log(route.paramMap.get(ID_AUTOLIQUIDACIO_PARAM));

    if (this.store.selfassessmentId || route.paramMap.get(ID_AUTOLIQUIDACIO_PARAM)) {
      return true;
    }
    return false;
  }
}