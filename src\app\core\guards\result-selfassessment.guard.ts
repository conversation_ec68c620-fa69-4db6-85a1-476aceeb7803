import { Injectable } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StoreService } from '../services';
import { AppRoutes, ID_AUTOLIQUIDACIO_PARAM } from '../models';

@Injectable({ providedIn: 'root' })
export class ResultSelfAssessmentGuard {
  constructor(private router: Router, private store: StoreService, private activeRoute: ActivatedRoute) {}

  canActivate() {
    debugger
    console.log(this.activeRoute.snapshot.paramMap.get(ID_AUTOLIQUIDACIO_PARAM));
    
    if (this.store.selfassessmentId || this.activeRoute.snapshot.paramMap.get(ID_AUTOLIQUIDACIO_PARAM)) {
      return true;
    } 
    return false;
  }
}