import { Injectable } from '@angular/core';
import { RequestDownloadFile, SeDocumentsService } from 'se-ui-components-mf-lib';
import { MenuItem } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';

import { LoginInfoService } from 'src/app/core/services/login-info.service';
import { ResultSelfAssessmentEndpointService } from './result-selfassessment-endpoint.service';

const BASE_TRANSLATION = `SE_GRANS_ESTABLIMENTS_MF.MODULE_RESULTAT`;

@Injectable({
  providedIn: 'root',
})
export class ResultSelfAssessmentService {
  private seuUrl = '';
  constructor(
    private seDocumentsService: SeDocumentsService,
    private translateService: TranslateService,
    private loginInfo: LoginInfoService,
    private resultSelfAssessmentEndpointService: ResultSelfAssessmentEndpointService,
  ) {}

  async getActions(
    idDocuments: string[],
    idAutoliquidacio: string,
  ): Promise<MenuItem[]> {
    this.seuUrl = await this.loginInfo.getSeuUrl();

    return [
      {
        checkDisabled: false,
        label: this.translateService.instant(
          `${BASE_TRANSLATION}.TABLE_ACTIONS.PAYMENT_RECEIPT`,
        ),
        command: (): void => {
          if (idAutoliquidacio) {
            this.resultSelfAssessmentEndpointService
              .postDownloadPaymentReceipt(idAutoliquidacio)
              .subscribe((response) => {
                if (response.content) {
                  this.seDocumentsService.downloadFile(
                    {id: response.content.idDocument},
                    response.content.nom,
                  );
                }
              });
          }
        },
      },
      {
        checkDisabled: false,
        label: this.translateService.instant(
          `${BASE_TRANSLATION}.TABLE_ACTIONS.VIEW_SELFASSESMENT`,
        ),
        command: (): void => {
          if (idDocuments.length > 0) {
            this.seDocumentsService.downloadFile({ id: idDocuments[0] }, '');
          }
        },
      },
      {
        checkDisabled: false,
        label: this.translateService.instant(
          `${BASE_TRANSLATION}.TABLE_ACTIONS.PAYMENT_POSTPONEMENT`,
        ),
        command: (event): void => {
          if (event.item) {
            const data = (event.item as MenuItem)['data'];
            if (data?.justificant?.value && data?.nif?.value && this.seuUrl) {
              window.open(
                `${this.seuUrl}/${this.translateService.currentLang}/OficinaVirtual/Pagines/TramitsGenerics.aspx`,
              );
            }
          }
        },
      },
    ];
  }
}
