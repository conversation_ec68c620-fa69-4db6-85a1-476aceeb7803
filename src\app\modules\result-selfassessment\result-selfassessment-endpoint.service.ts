import { Injectable } from '@angular/core';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

import { Observable } from 'rxjs';

interface PaymentReceiptResponse {
  nom: string;
  base64: string;
}

@Injectable({
  providedIn: 'root',
})
export class ResultSelfAssessmentEndpointService {
  constructor(private httpService: SeHttpService) {}

  postDownloadPaymentReceipt(
    idAutoliquidacio: string,
  ): Observable<SeHttpResponse<PaymentReceiptResponse>> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacio/${idAutoliquidacio}/carta-pagament`,
    });
  }
}
