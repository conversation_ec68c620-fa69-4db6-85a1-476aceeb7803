import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subject, lastValueFrom, map, take, takeUntil, tap } from 'rxjs';

import {
  SeAlertMessage,
  SeAlertType,
  SeAuthService,
  SeModalService,
  SeProgressModal,
} from 'se-ui-components-mf-lib';
import {
  AppRoutes,
  HeaderInfoService,
  StoreService,
  type SelfAssessmentStatus,
  ID_AUTOLIQUIDACIO_PARAM,
} from 'src/app/core';
import { AutoliquidacioError } from '../presentacio-i-pagament/models/autoliquidacio.model';
import { StatusAutoliquidacio } from '../presentacio-i-pagament/models/status-autoliquidacio.model';
import { MenuItem } from 'primeng/api';
import { PresentacioIPagamentsEndpointsService } from '../presentacio-i-pagament/presentacio-i-pagament-endpoints.service';
import { ResultSelfAssessmentService } from './result-selfassessment.service';

@Component({
  selector: 'app-result-selfassessment',
  templateUrl: './result-selfassessment.component.html',
  styleUrls: [],
})
export class ResultSelfAssessmentComponent implements OnInit {
  selfassessmentId: string | null = null;
  idDocuments: string[] | null = null;
  status: StatusAutoliquidacio | null = null;
  alertMessage: SeAlertMessage | null = null;
  alertDescription = '';
  headers = ['justificant', 'taxpayer', 'date', 'total', 'state'];

  private readonly familyProcedure = 'FAM3';
  private readonly procedureId = 'F3CAS18';
  private unsubscribe: Subject<void> = new Subject();
  actions: MenuItem[] | undefined;

  constructor(
    private storeService: StoreService,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: SeModalService,
    private translateService: TranslateService,
    private pipEndpointsService: PresentacioIPagamentsEndpointsService,
    private resultSelfassessmentService: ResultSelfAssessmentService,
    private cdRef: ChangeDetectorRef,
    private authService: SeAuthService,
    private header: HeaderInfoService,
  ) {}

  ngOnInit(): void {
    this.selfassessmentId = this.storeService.selfassessmentId ?? this.route.snapshot.paramMap.get(ID_AUTOLIQUIDACIO_PARAM);
    this.status =
      this.router.getCurrentNavigation()?.extras.state?.[
        'statusAutoliquidacio'
      ];
    this.checkStatus();
    this.setupSurvey();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  goToPagament = (): void => {
    this.router.navigate([AppRoutes.PAGAMENT]);
  };

  private checkStatus = async (): Promise<void> => {
    this.status = await lastValueFrom(
      this.pipEndpointsService
        .getStatusAutoliquidacio(this.selfassessmentId!)
        .pipe(
          take(1),
          tap(async (response) => {
            const { message, description } = this.getAlert(
              response.content.estat,
              response.content.errors,
            );
            this.alertMessage = message;
            this.alertDescription = description;
            const state = response?.content?.estat as SelfAssessmentStatus;
            this.header.status = state;
            this.header.presentationDate = response?.content?.dataPresentacio;
            this.idDocuments = response.content?.idDocuments;
          }),
          map((response) => response?.content.estat),
        ),
    );
    const amount = this.storeService.amountToPay;
    if (
      (this.status === StatusAutoliquidacio.PRESENTAT ||
        this.status === StatusAutoliquidacio.PENDENT_PAGAMENT ||
        this.status === StatusAutoliquidacio.PAGAMENT_ERROR) &&
      amount &&
      amount > 0
    ) {
      this.actions = await this.resultSelfassessmentService.getActions(
        this.idDocuments!,
        this.selfassessmentId!,
      );
      this.reloadView();
    }

    if (this.status === StatusAutoliquidacio.PAGANT) {
      const progressModal: SeProgressModal = {
        interval: 15,
        message: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_PRESENTACION_I_PAGAMENT.PAGAMENT.MODAL_MESSAGE',
        ),
      };

      const modalRef = this.modalService.openProgressModal(
        progressModal.interval!,
        progressModal.message!,
      );

      modalRef.componentInstance.intervalOutput
        .pipe(takeUntil(this.unsubscribe))
        .subscribe(() => {
          this.pipEndpointsService
            .getStatusAutoliquidacio(this.selfassessmentId!)
            .pipe(takeUntil(this.unsubscribe))
            .subscribe({
              next: (response) => {
                this.header.status = response?.content
                  ?.estat as SelfAssessmentStatus;
                this.header.presentationDate =
                  response?.content?.dataPresentacio;
                if (
                  response?.content.estat === StatusAutoliquidacio.PAGAT ||
                  response?.content.estat ===
                    StatusAutoliquidacio.PAGAMENT_ERROR ||
                  response?.content.estat === StatusAutoliquidacio.ERROR
                ) {
                  const { message, description } = this.getAlert(
                    response?.content.estat,
                  );
                  this.alertMessage = message;
                  this.alertDescription = description;

                  modalRef.close();
                  this.reloadView();
                }
              },
              error: () => {
                modalRef.close();
              },
            });
        });
    }
  };

  private getAlert(
    status: StatusAutoliquidacio,
    errors: AutoliquidacioError[] = [],
  ): { message: SeAlertMessage | null; description: string } {
    const translation = `SE_GRANS_ESTABLIMENTS_MF.MODULE_PRESENTACION_I_PAGAMENT.DOCUMENTATION`;
    const alert: SeAlertMessage = {
      title: '',
      type: SeAlertType.SUCCESS,
      list: [],
      id: this.status!,
    };

    const alertSuccess = {
      ...alert,
      title: `${translation}.ALERT_TITLE_SUCCESS`,
    };

    const alertError = {
      ...alert,
      type: SeAlertType.ERROR,
    };

    switch (status) {
      case StatusAutoliquidacio.PAGAT:
        return {
          message: alertSuccess,
          description: `${translation}.ALERT_DESCRIPTION_PAGAT_SUCCESS`,
        };
      case StatusAutoliquidacio.PENDENT_PAGAMENT:
      case StatusAutoliquidacio.PRESENTAT:
        return {
          message: alertSuccess,
          description: `${translation}.ALERT_DESCRIPTION_PRESENTAT_PENDENT_SUCCESS`,
        };

      case StatusAutoliquidacio.PAGAMENT_ERROR:
      case StatusAutoliquidacio.ERROR:
        return {
          message: {
            ...alertError,
            title: `${translation}.ALERT_TITLE_PAGAMENT_ERROR`,
            list: this.getOneSiteErrors(errors),
          },
          description: `${translation}.ALERT_DESCRIPTION_PAGAMENT_ERROR`,
        };
      case StatusAutoliquidacio.PRESENTACIO_ERROR:
        return {
          message: {
            ...alertError,
            title: `${translation}.ALERT_TITLE_PRESENTACIO_ERROR`,
          },
          description: `${translation}.ALERT_DESCRIPTION_PRESENTACIO_ERROR`,
        };

      default:
        return { message: null, description: '' };
    }
  }

  private setupSurvey(): void {
    const eventDetail = {
      familia: this.familyProcedure,
      tramite: this.procedureId,
      querySelector: 'app-result-selfassessment',
      produccion:
        this.authService.getSessionStorageUser().environment === 'pro',
    };

    document.dispatchEvent(
      new CustomEvent('showSurveyEvent', { detail: eventDetail }),
    );
  }

  private getOneSiteErrors(errors: AutoliquidacioError[]): string[] {
    return errors.map(
      (error) => `UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${error.code}`,
    );
  }

  reloadView = (): void => {
    const aux = this.selfassessmentId;
    this.selfassessmentId = null;
    this.cdRef.detectChanges();
    this.selfassessmentId = aux;
  };
}
